/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    padding: 30px 0;
    border-bottom: 3px solid #2c5aa0;
    margin-bottom: 30px;
}

header h1 {
    color: #2c5aa0;
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

header h2 {
    color: #666;
    font-size: 1.5em;
    font-weight: normal;
    margin-bottom: 20px;
}

/* 导航样式 */
nav {
    margin-top: 20px;
}

.nav-link {
    display: inline-block;
    padding: 10px 20px;
    margin: 0 10px;
    background: #2c5aa0;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: bold;
}

.nav-link:hover {
    background: #1e3d6f;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 首页样式 */
.logo-options {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 30px;
    margin: 40px 0;
}

.option-card {
    flex: 1;
    min-width: 300px;
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid #e0e0e0;
}

.option-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    border-color: #2c5aa0;
}

.option-card h3 {
    color: #2c5aa0;
    font-size: 1.8em;
    margin-bottom: 20px;
}

.option-preview {
    margin: 20px 0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.preview-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.preview-image:hover {
    transform: scale(1.05);
}

.option-button {
    background: linear-gradient(45deg, #2c5aa0, #4a7bc8);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1em;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.option-button:hover {
    background: linear-gradient(45deg, #1e3d6f, #2c5aa0);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
}

/* 方案页面样式 */
.scheme-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
    margin: 30px 0;
}

.image-section {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.image-section h3 {
    color: #2c5aa0;
    font-size: 1.5em;
    margin-bottom: 20px;
    text-align: center;
}

.image-container {
    text-align: center;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.scheme-image {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: contain;
    background: white;
}

.content-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.content-section h3 {
    color: #2c5aa0;
    font-size: 1.8em;
    margin-bottom: 25px;
    border-bottom: 2px solid #2c5aa0;
    padding-bottom: 10px;
}

.text-content h4 {
    color: #1e3d6f;
    font-size: 1.3em;
    margin: 25px 0 15px 0;
    padding-left: 15px;
    border-left: 4px solid #2c5aa0;
}

.text-content h5 {
    color: #2c5aa0;
    font-size: 1.1em;
    margin: 20px 0 10px 0;
}

.text-content p {
    margin-bottom: 15px;
    text-align: justify;
    line-height: 1.8;
}

.text-content ul {
    margin: 15px 0;
    padding-left: 30px;
}

.text-content li {
    margin-bottom: 10px;
    line-height: 1.7;
}

.color-scheme, .color-analysis, .letter-analysis {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    border-left: 4px solid #2c5aa0;
}

/* 底部样式 */
footer {
    text-align: center;
    padding: 20px 0;
    border-top: 2px solid #e0e0e0;
    margin-top: 40px;
    color: #666;
    font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .logo-options {
        flex-direction: column;
    }
    
    .option-card {
        min-width: auto;
    }
    
    .scheme-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .nav-link {
        display: block;
        margin: 5px 0;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.5em;
    }
    
    header h2 {
        font-size: 1.2em;
    }
    
    .option-button {
        padding: 12px 20px;
        font-size: 1em;
    }
}
